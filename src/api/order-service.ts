import { fetcher, revolvedCreator } from "@/lib/axios";
import { API_ENDPOINTS } from "./api-endpoints";
import { CreateOrderData, OrdersGetResponse, OrderDetailGetResponse, OrderStatus, PaymentStatus } from "@/types/order";
import useSWR, { mutate, SWRConfiguration } from "swr";
import { useMemo } from "react";

const swrOptions: SWRConfiguration = {
    // revalidateIfStale: false,
    revalidateOnFocus: false,
    // revalidateOnReconnect: false,
};

interface GetOrderListParams {
    limit?: number;
    searchQuery?: string;
    page?: number;
    status?: OrderStatus;
    paymentStatus?: PaymentStatus;
}

export function useGetOrderList({
    page = 0,
    limit = 10,
    searchQuery = "",
    status,
    paymentStatus,
}: GetOrderListParams) {
    const getTheFullUrl = () => {
        const queryParams: { [key: string]: any } = {
            page: page + 1, // API uses 1-based indexing
            limit,
        };

        if (searchQuery !== "") queryParams.search = searchQuery;
        if (status) queryParams.status = status;
        if (paymentStatus) queryParams.paymentStatus = paymentStatus;

        return `${API_ENDPOINTS.order.getList}?${new URLSearchParams(queryParams)}`;
    };

    const { data, isLoading, error, isValidating } = useSWR<OrdersGetResponse>(
        getTheFullUrl,
        fetcher,
        swrOptions
    );

    const memoizedValue = useMemo(
        () => ({
            orders: data?.data?.data || [],
            ordersLoading: isLoading,
            ordersError: error,
            ordersValidating: isValidating,
            ordersEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
            totalCount: data?.data?.totalCount ?? 0,
            totalPages: data?.data?.totalPages ?? 0,
            currentPage: data?.data?.currentPage ?? 1,
        }),
        [data?.data, error, isLoading, isValidating]
    );

    const revalidateOrdersList = () => {
        mutate(getTheFullUrl());
    };

    return {
        ...memoizedValue,
        revalidateOrdersList,
    };
}

export function useGetOrderDetail({ orderId }: { orderId: string }) {
    const getTheFullUrl = () => {
        if (!orderId) return null;
        return `${API_ENDPOINTS.order.getDetails}/${orderId}`;
    };

    const { data, isLoading, error, isValidating } = useSWR<OrderDetailGetResponse>(
        getTheFullUrl,
        fetcher,
        swrOptions
    );

    const memoizedValue = useMemo(
        () => ({
            order: data?.data,
            orderLoading: isLoading,
            orderError: error,
            orderValidating: isValidating,
        }),
        [data?.data, error, isLoading, isValidating]
    );

    const revalidateOrder = () => {
        mutate(getTheFullUrl());
    };

    return {
        ...memoizedValue,
        revalidateOrder,
    };
}

export function createOrder(orderData: CreateOrderData) {
    const url = API_ENDPOINTS.order.create;
    const result = revolvedCreator([url, orderData]);
    return result;
}