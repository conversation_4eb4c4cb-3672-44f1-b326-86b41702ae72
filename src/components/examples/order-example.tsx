"use client";

import { useState } from "react";
import { useGetOrderList, useGetOrderDetail, createOrder } from "@/api/order-service";
import { Order, CreateOrderData } from "@/types/order";
import { Button } from "@/components/ui/button";
import { useToast } from "@/context/toast-context";

// Example Order data based on your JSON structure
const exampleOrderData: Order = {
  _id: "686e20ab0e007e6de01deed3",
  user: "67f38ab49bcb60e9a297d97c",
  deliveryAddress: "6824785846b5f1bf6458d6bb",
  orderReference: "OR-090725-7378",
  totalPrice: 930,
  subTotal: 930,
  discountPrice: 0,
  taxAmount: 0,
  additionalCharges: 0,
  paymentStatus: "pending",
  status: "placed",
  fulfillment: {
    status: "pending",
    shippingCost: 0,
    _id: "686e20ab0e007e6de01deed7"
  },
  createdAt: "2025-07-09T07:56:27.784Z",
  updatedAt: "2025-07-09T07:56:27.784Z",
  __v: 0,
  userDetails: [
    {
      _id: "67f38ab49bcb60e9a297d97c",
      email: "<EMAIL>",
      name: "aruna nuwantha 6"
    }
  ]
};

const OrderExample = () => {
  const { showToast } = useToast();
  const [selectedOrderId, setSelectedOrderId] = useState<string>("");
  
  // Get orders list
  const { 
    orders, 
    ordersLoading, 
    ordersError, 
    totalCount,
    revalidateOrdersList 
  } = useGetOrderList({
    page: 0,
    limit: 10,
    status: "placed"
  });

  // Get specific order detail
  const { 
    order, 
    orderLoading, 
    orderError,
    revalidateOrder 
  } = useGetOrderDetail({ 
    orderId: selectedOrderId 
  });

  // Handle creating a new order
  const handleCreateOrder = async () => {
    try {
      const orderData: CreateOrderData = {
        deliveryAddress: "6824785846b5f1bf6458d6bb",
        paymentMethod: "card",
        notes: "Please handle with care"
      };

      const result = await createOrder(orderData);
      showToast("Order created successfully!", "success");
      revalidateOrdersList();
      console.log("Order created:", result);
    } catch (error) {
      showToast("Failed to create order", "error");
      console.error("Error creating order:", error);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Order Management Example</h1>
      
      {/* Example Order Data Display */}
      <div className="mb-8 p-4 bg-gray-50 rounded-lg">
        <h2 className="text-lg font-semibold mb-4">Example Order Structure</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <p><strong>Order ID:</strong> {exampleOrderData._id}</p>
            <p><strong>Reference:</strong> {exampleOrderData.orderReference}</p>
            <p><strong>Status:</strong> {exampleOrderData.status}</p>
            <p><strong>Payment Status:</strong> {exampleOrderData.paymentStatus}</p>
          </div>
          <div>
            <p><strong>Total Price:</strong> ${exampleOrderData.totalPrice}</p>
            <p><strong>Subtotal:</strong> ${exampleOrderData.subTotal}</p>
            <p><strong>Tax:</strong> ${exampleOrderData.taxAmount}</p>
            <p><strong>Created:</strong> {new Date(exampleOrderData.createdAt).toLocaleDateString()}</p>
          </div>
        </div>
        {exampleOrderData.userDetails && (
          <div className="mt-4">
            <p><strong>Customer:</strong> {exampleOrderData.userDetails[0]?.name}</p>
            <p><strong>Email:</strong> {exampleOrderData.userDetails[0]?.email}</p>
          </div>
        )}
      </div>

      {/* Create Order Button */}
      <div className="mb-6">
        <Button onClick={handleCreateOrder} variant="primary">
          Create New Order
        </Button>
      </div>

      {/* Orders List */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Orders List ({totalCount})</h2>
          <Button onClick={revalidateOrdersList} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
        
        {ordersLoading && <p>Loading orders...</p>}
        {ordersError && <p className="text-red-500">Error: {ordersError.message}</p>}
        
        <div className="space-y-2">
          {orders.map((order) => (
            <div 
              key={order._id} 
              className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
              onClick={() => setSelectedOrderId(order._id)}
            >
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Order #{order.orderReference}</p>
                  <p className="text-sm text-gray-600">
                    {new Date(order.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-medium">${order.totalPrice}</p>
                  <p className="text-sm text-gray-600 capitalize">{order.status}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Order Detail */}
      {selectedOrderId && (
        <div>
          <h2 className="text-lg font-semibold mb-4">Order Detail</h2>
          {orderLoading && <p>Loading order detail...</p>}
          {orderError && <p className="text-red-500">Error: {orderError.message}</p>}
          {order && (
            <div className="p-4 border rounded-lg bg-blue-50">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p><strong>Order ID:</strong> {order._id}</p>
                  <p><strong>Reference:</strong> {order.orderReference}</p>
                  <p><strong>Status:</strong> {order.status}</p>
                  <p><strong>Payment:</strong> {order.paymentStatus}</p>
                </div>
                <div>
                  <p><strong>Total:</strong> ${order.totalPrice}</p>
                  <p><strong>Fulfillment:</strong> {order.fulfillment.status}</p>
                  <p><strong>Shipping:</strong> ${order.fulfillment.shippingCost}</p>
                </div>
              </div>
              {order.products && order.products.length > 0 && (
                <div className="mt-4">
                  <p><strong>Products:</strong></p>
                  <ul className="list-disc list-inside ml-4">
                    {order.products.map((product) => (
                      <li key={product._id}>{product.name} - ${product.price}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OrderExample;
